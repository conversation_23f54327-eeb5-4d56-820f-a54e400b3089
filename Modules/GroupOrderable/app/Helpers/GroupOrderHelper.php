<?php

namespace Modules\GroupOrderable\Helpers;
use Illuminate\Database\Eloquent\Model;
use Modules\GroupOrderable\Models\GroupItemPivot;
use Illuminate\Pagination\LengthAwarePaginator;

class GroupOrderHelper
{
    public function attach(Model $group, Model $item, ?int $position = null): void
    {
        $defaultPos = config('grouporderable.default_position', 99);

        GroupItemPivot::create([
            'group_id'   => $group->id,
            'group_able' => $group->getMorphClass(),
            'item_id'    => $item->id,
            'item_able'  => $item->getMorphClass(),
            'position'   => $position ?? $defaultPos,
        ]);
    }

    public function detach(Model $group, Model $item): void
    {
        GroupItemPivot::where('group_id', $group->id)
            ->where('group_able', $group->getMorphClass())
            ->where('item_id', $item->id)
            ->where('item_able', $item->getMorphClass())
            ->delete();
    }

    public function listItems(Model $group, ?int $per_page = 25, ?\Closure $itemFilter = null, $select = ['*']): LengthAwarePaginator
    {
        $pivotPaginator = GroupItemPivot::query()
            ->with(['item' => function ($q) use ($select) {
                if ($select !== ['*']) {
                    $model = $q->getModel();
                    $key = $model->getKeyName();

                    if (!in_array($key, $select)) {
                        $select[] = $key;
                    }

                    $q->select($select);
                }
            }])
            ->when($itemFilter, function ($q) use ($itemFilter) {
                $q->whereHas('item', $itemFilter);
            })
            ->where('group_id', $group->id)
            ->where('group_able', $group->getMorphClass())
            ->orderBy('position')
            ->orderBy('id')
            ->paginate($per_page);

        $entities = $pivotPaginator->getCollection()
            ->map(fn($pivot) => $pivot->item)
            ->filter();

        return new LengthAwarePaginator(
            $entities,
            $pivotPaginator->total(),
            $pivotPaginator->perPage(),
            $pivotPaginator->currentPage(),
            [
                'path' => request()->url(),
                'query' => request()->query(),
            ]
        );
    }
}
