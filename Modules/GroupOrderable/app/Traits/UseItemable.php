<?php

namespace Modules\GroupOrderable\Traits;
use Illuminate\Support\Facades\DB;

trait UseItemable
{
    protected function groupItemPivotTable(): string
    {
        return 'group_items';
    }

    protected function defaultPivotPosition(): int
    {
        return config('grouporderable.default_position', 99);
    }

    public static function bootUseItemable(): void
    {
        static::deleting(function ($model) {
            $model->resequenceAndDetachAllPivotsForThisItem();
        });
    }

    protected function resequenceAndDetachAllPivotsForThisItem(): void
    {
        $pivotTable = $this->groupItemPivotTable();
        $defaultPos = $this->defaultPivotPosition();
        $itemId     = $this->getKey();
        $itemAble   = $this->getMorphClass(); // tôn trọng morphMap

        DB::transaction(function () use ($pivotTable, $defaultPos, $itemId, $itemAble) {
            // Khóa các hàng pivot thuộc item này để tính position an toàn
            $rows = DB::table($pivotTable)
                ->select('group_id', 'group_type', 'position')
                ->where('item_id', $itemId)
                ->where('item_type', $itemAble)
                ->lockForUpdate()
                ->get();

            // Dồn lại cho từng group mà item này có mặt
            foreach ($rows as $row) {
                $pos = (int) $row->position;

                // Chỉ dồn nếu item đang có "vị trí thật" (< defaultPos)
                if ($pos < $defaultPos) {
                    DB::table($pivotTable)
                        ->where('group_id', $row->group_id)
                        ->where('group_type', $row->group_type)
                        ->where('item_type', $itemAble)
                        ->where('position', '<', $defaultPos)
                        ->where('position', '>', $pos)
                        ->update([
                            'position' => DB::raw('position - 1')
                        ]);
                }
            }

            // Xóa toàn bộ pivot của item này (kể cả các hàng position=defaultPos)
            DB::table($pivotTable)
                ->where('item_id', $itemId)
                ->where('item_type', $itemAble)
                ->delete();
        });
    }

    public function moveToTop(int $groupId, string $groupAble): void
    {
        $this->moveToPosition($groupId, $groupAble, 1);
    }

    public function moveAfter(
        int $groupId,
        string $groupAble,
        int $afterItemId,
        ?string $afterItemAble = null
    ): void {
        $pivotTable = $this->groupItemPivotTable();
        $defaultPos = $this->defaultPivotPosition();
        $afterItemAble = $afterItemAble ?: $this->getMorphClass();

        DB::transaction(function () use ($groupId, $groupAble, $afterItemId, $afterItemAble, $pivotTable, $defaultPos) {
            // Khoá mục tiêu "after" để đọc vị trí
            $after = DB::table($pivotTable)
                ->where('group_id', $groupId)
                ->where('group_type', $groupAble)
                ->where('item_id', $afterItemId)
                ->where('item_type', $afterItemAble)
                ->lockForUpdate()
                ->first();

            // Đếm số item đã có vị trí thật (< defaultPos)
            $realCount = DB::table($pivotTable)
                ->where('group_id', $groupId)
                ->where('group_type', $groupAble)
                ->where('item_type', $this->getMorphClass())
                ->where('position', '<', $defaultPos)
                ->count();

            // Nếu after không tồn tại hoặc after đang ở 99 => đẩy xuống cuối dải
            if (!$after || (int)$after->position >= $defaultPos) {
                $newPos = $realCount + 1;
            } else {
                $newPos = ((int)$after->position) + 1;
                // chặn tràn nếu after là cuối cùng
                if ($newPos > $realCount + 1) {
                    $newPos = $realCount + 1;
                }
            }

            // Thực hiện dịch chuyển item hiện tại đến $newPos
            $this->moveToPosition($groupId, $groupAble, $newPos);
        });
    }

    protected function moveToPosition(int $groupId, string $groupAble, int $newPos): void
    {
        $pivotTable = $this->groupItemPivotTable();
        $defaultPos = $this->defaultPivotPosition();
        $itemId     = $this->getKey();
        $itemAble   = $this->getMorphClass();

        DB::transaction(function () use ($pivotTable, $defaultPos, $groupId, $groupAble, $itemId, $itemAble, $newPos) {
            // Đếm số phần tử đã có vị trí thật
            $realCount = DB::table($pivotTable)
                ->where('group_id', $groupId)
                ->where('group_type', $groupAble)
                ->where('item_type', $itemAble)
                ->where('position', '<', $defaultPos)
                ->lockForUpdate()
                ->count();

            // Lấy (hoặc tạo) pivot của item hiện tại
            $pivot = DB::table($pivotTable)
                ->where('group_id', $groupId)
                ->where('group_type', $groupAble)
                ->where('item_id', $itemId)
                ->where('item_type', $itemAble)
                ->lockForUpdate()
                ->first();

            if (!$pivot) {
                // Tạo pivot mới với position mặc định
                DB::table($pivotTable)->insert([
                    'group_id'   => $groupId,
                    'group_type' => $groupAble,
                    'item_id'    => $itemId,
                    'item_type'  => $itemAble,
                    'position'   => $defaultPos,
                ]);
                $pivot = (object)[
                    'group_id' => $groupId,
                    'group_type' => $groupAble,
                    'item_id' => $itemId,
                    'item_type' => $itemAble,
                    'position' => $defaultPos,
                ];
            }

            $currentPos = (int)$pivot->position;

            // Kẹp biên newPos
            $maxInsert = $realCount + ($currentPos >= $defaultPos ? 1 : 0);
            $newPos    = max(1, min($newPos, max(1, $maxInsert)));

            if ($currentPos >= $defaultPos) {
                // Đang ở "hàng chờ" 99 -> chèn vào dải thật tại newPos
                DB::table($pivotTable)
                    ->where('group_id', $groupId)
                    ->where('group_type', $groupAble)
                    ->where('item_type', $itemAble)
                    ->where('position', '<', $defaultPos)
                    ->where('position', '>=', $newPos)
                    ->update(['position' => DB::raw('position + 1')]);

                DB::table($pivotTable)
                    ->where('group_id', $groupId)
                    ->where('group_type', $groupAble)
                    ->where('item_id', $itemId)
                    ->where('item_type', $itemAble)
                    ->update(['position' => $newPos]);

                return;
            }

            // Nếu đã ở dải thật
            if ($newPos == $currentPos) {
                return; // không cần làm gì
            }

            if ($newPos < $currentPos) {
                // Kéo lên: dồn [newPos .. currentPos-1] xuống 1
                DB::table($pivotTable)
                    ->where('group_id', $groupId)
                    ->where('group_type', $groupAble)
                    ->where('item_type', $itemAble)
                    ->where('position', '<', $defaultPos)
                    ->whereBetween('position', [$newPos, $currentPos - 1])
                    ->update(['position' => DB::raw('position + 1')]);
            } else {
                // Kéo xuống: kéo [currentPos+1 .. newPos] lên 1
                DB::table($pivotTable)
                    ->where('group_id', $groupId)
                    ->where('group_type', $groupAble)
                    ->where('item_type', $itemAble)
                    ->where('position', '<', $defaultPos)
                    ->whereBetween('position', [$currentPos + 1, $newPos])
                    ->update(['position' => DB::raw('position - 1')]);
            }

            // Cập nhật vị trí của chính nó
            DB::table($pivotTable)
                ->where('group_id', $groupId)
                ->where('group_type', $groupAble)
                ->where('item_id', $itemId)
                ->where('item_type', $itemAble)
                ->update(['position' => $newPos]);
        });
    }
}
