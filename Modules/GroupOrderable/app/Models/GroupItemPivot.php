<?php

namespace Modules\GroupOrderable\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
// use Modules\GroupOrderable\Database\Factories\GroupItemPivotFactory;

/**
 * @property int $id
 * @property int $group_id
 * @property string $group_type
 * @property int $item_id
 * @property string $item_type
 * @property int $position
 */

class GroupItemPivot extends Model
{
    use HasFactory;

    protected $table = 'group_items';

    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'group_id',
        'group_type',
        'item_id',
        'item_type',
        'position',
    ];

    public function group()
    {
        return $this->morphTo('group');
    }

    public function item()
    {
        return $this->morphTo('item');
    }
}
