<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('group_items', function (Blueprint $table) {
            $table->id();
            $table->id();
            $table->morphs('group');
            $table->morphs('item');
            $table->unsignedInteger('position')->default(99);
            $table->timestamps();

            $table->unique(['group_id', 'group_type', 'item_id', 'item_type'], 'uniq_group_item');
            $table->index(['group_id', 'group_type', 'position']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('group_items');
    }
};
