<?php

namespace Modules\Storage\Services;

use Aws\S3\S3Client;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Log;
use League\Flysystem\AwsS3V3\AwsS3V3Adapter;
use League\Flysystem\Filesystem;
use League\Flysystem\FilesystemException;
use Modules\Storage\Events\FileDeleted;
use Modules\Storage\Events\StorageError;
use Modules\Storage\Models\File;
use Illuminate\Database\Eloquent\Model;

class StorageService
{
    protected S3Client $s3Client;
    protected Filesystem $filesystem;
    protected array $config;

    public function __construct()
    {
        $this->config = config('storage.r2');
        $this->initializeS3Client();
        $this->initializeFilesystem();
    }

    /**
     * Initialize S3 client for Cloudflare R2
     */
    protected function initializeS3Client(): void
    {
        // Validate required credentials
        if (empty($this->config['key']) || empty($this->config['secret'])) {
            throw new \InvalidArgumentException('R2 credentials (key/secret) are required but not set. Check your R2_ACCESS_KEY_ID and R2_SECRET_ACCESS_KEY environment variables.');
        }

        if (empty($this->config['endpoint'])) {
            throw new \InvalidArgumentException('R2 endpoint is required but not set. Check your R2_ENDPOINT environment variable.');
        }

        if (empty($this->config['bucket'])) {
            throw new \InvalidArgumentException('R2 bucket is required but not set. Check your R2_BUCKET environment variable.');
        }

        $this->s3Client = new S3Client([
            'version' => 'latest',
            'region' => $this->config['region'],
            'endpoint' => trim($this->config['endpoint']), // Trim any whitespace
            'use_path_style_endpoint' => $this->config['use_path_style_endpoint'],
            'credentials' => [
                'key' => trim($this->config['key']), // Trim any whitespace
                'secret' => trim($this->config['secret']), // Trim any whitespace
            ],
        ]);
    }

    /**
     * Initialize Flysystem filesystem
     */
    protected function initializeFilesystem(): void
    {
        $adapter = new AwsS3V3Adapter(
            $this->s3Client,
            $this->config['bucket']
        );

        $this->filesystem = new Filesystem($adapter);
    }

    /**
     * Delete a file from R2
     */
    public function deleteFile(File $file): bool
    {
        try {
            if (!$this->filesystem->fileExists($file->path)) {
                Log::warning('Attempted to delete non-existent file', ['path' => $file->path]);
            }

            $this->filesystem->delete($file->path);
            $file->delete();

            event(new FileDeleted(['path' => $file->path]));

            Log::info('File deleted successfully', ['path' => $file->path]);

            return true;

        } catch (FilesystemException $e) {
            $error = [
                'operation' => 'delete',
                'error' => $e->getMessage(),
                'path' => $file->path,
            ];

            event(new StorageError($error));

            Log::error('File deletion failed', $error);

            throw $e;
        }
    }

    /**
     * Generate presigned URL for upload
     */
    public function generatePresignedUploadUrl(string $path, string $contentType, int $expiresIn = 3600): string
    {
        try {
            $command = $this->s3Client->getCommand('PutObject', [
                'Bucket' => $this->config['bucket'],
                'Key' => $path,
                'ContentType' => $contentType,
            ]);

            $request = $this->s3Client->createPresignedRequest($command, "+{$expiresIn} seconds");

            return (string) $request->getUri();

        } catch (\Exception $e) {
            $error = [
                'operation' => 'presigned_upload_url',
                'error' => $e->getMessage(),
                'path' => $path,
            ];

            event(new StorageError($error));

            Log::error('Failed to generate presigned upload URL', $error);

            throw $e;
        }
    }

    /**
     * Generate presigned URL for download
     */
    public function generatePresignedDownloadUrl(string $path, int $expiresIn = 900): string
    {
        try {
            $command = $this->s3Client->getCommand('GetObject', [
                'Bucket' => $this->config['bucket'],
                'Key' => $path,
            ]);

            $request = $this->s3Client->createPresignedRequest($command, "+{$expiresIn} seconds");

            return (string) $request->getUri();

        } catch (\Exception $e) {
            $error = [
                'operation' => 'presigned_download_url',
                'error' => $e->getMessage(),
                'path' => $path,
            ];

            event(new StorageError($error));

            Log::error('Failed to generate presigned download URL', $error);

            throw $e;
        }
    }

    /**
     * Generate presigned URL for temporary upload with auto-generated filename
     */
    public function presignedTempUploadUrl(string $extension, int $expiresIn = 3600): array
    {
        try {
            $filename = $this->generateUniqueFilename($extension);
            $fullPath = $this->buildFilePath('tmp', $filename);
            $command = $this->s3Client->getCommand('PutObject', [
                'Bucket' => $this->config['bucket'],
                'Key' => $fullPath,
                'ContentType' => $this->getMimeTypeFromExtension($extension),
            ]);

            $request = $this->s3Client->createPresignedRequest($command, "+{$expiresIn} seconds");

            $presignedUrl = (string) $request->getUri();
            $publicUrl = $this->getPublicUrl($fullPath);

            return [
                'presigned_url' => $presignedUrl,
                'public_url' => $publicUrl,
                'file_path' => $fullPath,
                'filename' => $filename
            ];

        } catch (\Exception $e) {
            $error = [
                'operation' => 'presigned_temp_upload_url',
                'error' => $e->getMessage(),
                'extension' => $extension,
            ];

            event(new StorageError($error));

            Log::error('Failed to generate presigned temp upload URL', $error);

            throw $e;
        }
    }

    /**
     * Get public URL for a file
     */
    public function getPublicUrl(string $path): string
    {
        $publicUrl = $this->config['public_url'] ?? $this->config['endpoint'];
        return rtrim($publicUrl, '/') . '/' . ltrim($path, '/');
    }

    /**
     * Get MIME type from file extension
     */
    private function getMimeTypeFromExtension(string $extension): string
    {
        $extension = strtolower(ltrim($extension, '.'));

        $mimeTypes = [
            // Images
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'webp' => 'image/webp',
            'svg' => 'image/svg+xml',

            // Documents
            'pdf' => 'application/pdf',
            'doc' => 'application/msword',
            'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'xls' => 'application/vnd.ms-excel',
            'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'ppt' => 'application/vnd.ms-powerpoint',
            'pptx' => 'application/vnd.openxmlformats-officedocument.presentationml.presentation',

            // Audio/Video
            'mp3' => 'audio/mpeg',
            'wav' => 'audio/wav',
            'mp4' => 'video/mp4',
            'avi' => 'video/x-msvideo',
            'mov' => 'video/quicktime',

            // Text
            'txt' => 'text/plain',
            'json' => 'application/json',
            'xml' => 'application/xml',
            'csv' => 'text/csv',

            // Archives
            'zip' => 'application/zip',
            'rar' => 'application/vnd.rar',
            '7z' => 'application/x-7z-compressed',
        ];

        return $mimeTypes[$extension] ?? 'application/octet-stream';
    }

    /**
     * Check if file exists
     */
    public function fileExists(string $path): bool
    {
        try {
            return $this->filesystem->fileExists($path);
        } catch (FilesystemException $e) {
            Log::error('Failed to check file existence', [
                'path' => $path,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Generate unique filename with file extension
     */
    public function generateUniqueFilename(string $extension): string
    {
        // Remove leading dot from extension if present
        $extension = ltrim($extension, '.');

        // Generate a unique filename with timestamp and random string
        $timestamp = time();
        $random = bin2hex(random_bytes(8));

        return "file_{$timestamp}_{$random}.{$extension}";
    }

    /**
     * Generate unique filename from uploaded file (backward compatibility)
     */
    protected function generateUniqueFilenameFromFile(UploadedFile $file): string
    {
        $extension = $file->getClientOriginalExtension();
        $basename = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $sanitizedBasename = preg_replace('/[^a-zA-Z0-9_-]/', '_', $basename);

        return $sanitizedBasename . '_' . uniqid() . '.' . $extension;
    }

    /**
     * Build full file path
     */
    protected function buildFilePath(?string $path, string $filename): string
    {
        $basePath = $path ?: config('storage.paths.uploads');
        return trim($basePath, '/') . '/' . $filename;
    }

    /**
     * Convert a full URL to a local file path by removing the domain/base URL
     */
    protected function convertUrlToLocalPath(string $urlOrPath): string
    {
        // If it's already a local path (doesn't start with http), return as is
        if (!str_starts_with($urlOrPath, 'http')) {
            return $urlOrPath;
        }

        // Parse the URL to extract the path component
        $parsedUrl = parse_url($urlOrPath);

        if (!$parsedUrl || !isset($parsedUrl['path'])) {
            throw new \InvalidArgumentException("Invalid URL provided: {$urlOrPath}");
        }

        // Remove leading slash to get relative path
        $localPath = ltrim($parsedUrl['path'], '/');

        Log::debug('Converted URL to local path', [
            'original_url' => $urlOrPath,
            'local_path' => $localPath
        ]);

        return $localPath;
    }

    /**
     * Move a file from one location to another
     */
    public function moveFile(string $sourcePath, string $destinationPath): bool
    {
        try {
            if (!$this->filesystem->fileExists($sourcePath)) {
                Log::warning('Attempted to move non-existent file', ['source' => $sourcePath]);
                return false;
            }

            if ($this->filesystem->fileExists($destinationPath)) {
                Log::warning('Destination file already exists', ['destination' => $destinationPath]);
                return false;
            }

            // Use R2-compatible copy method then delete source
            $this->copyFileForR2($sourcePath, $destinationPath);
            $this->filesystem->delete($sourcePath);

            Log::info('File moved successfully', [
                'source' => $sourcePath,
                'destination' => $destinationPath
            ]);

            return true;

        } catch (FilesystemException $e) {
            $error = [
                'operation' => 'move',
                'error' => $e->getMessage(),
                'source' => $sourcePath,
                'destination' => $destinationPath,
            ];

            event(new StorageError($error));

            Log::error('File move failed', $error);

            throw $e;
        }
    }

    /**
     * Copy a file from one location to another using S3 client directly to avoid ACL issues
     */
    public function copyFileForR2(string $sourcePath, string $destinationPath): bool
    {
        try {
            if (!$this->filesystem->fileExists($sourcePath)) {
                Log::warning('Attempted to copy non-existent file', ['source' => $sourcePath]);
                return false;
            }

            if ($this->filesystem->fileExists($destinationPath)) {
                Log::warning('Destination file already exists', ['destination' => $destinationPath]);
                return false;
            }

            // Use S3 client directly to copy without ACL operations
            $this->s3Client->copyObject([
                'Bucket' => $this->config['bucket'],
                'Key' => $destinationPath,
                'CopySource' => $this->config['bucket'] . '/' . $sourcePath,
                'MetadataDirective' => 'COPY',
            ]);

            Log::info('File copied successfully using S3 client', [
                'source' => $sourcePath,
                'destination' => $destinationPath
            ]);

            return true;

        } catch (\Exception $e) {
            $error = [
                'operation' => 'copy_for_r2',
                'error' => $e->getMessage(),
                'source' => $sourcePath,
                'destination' => $destinationPath,
            ];

            event(new StorageError($error));

            Log::error('File copy failed using S3 client', $error);

            throw $e;
        }
    }

    /**
     * Copy a file from one location to another (original method using Flysystem)
     */
    public function copyFile(string $sourcePath, string $destinationPath): bool
    {
        try {
            if (!$this->filesystem->fileExists($sourcePath)) {
                Log::warning('Attempted to copy non-existent file', ['source' => $sourcePath]);
                return false;
            }

            if ($this->filesystem->fileExists($destinationPath)) {
                Log::warning('Destination file already exists', ['destination' => $destinationPath]);
                return false;
            }

            $this->filesystem->copy($sourcePath, $destinationPath);

            Log::info('File copied successfully', [
                'source' => $sourcePath,
                'destination' => $destinationPath
            ]);

            return true;

        } catch (FilesystemException $e) {
            $error = [
                'operation' => 'copy',
                'error' => $e->getMessage(),
                'source' => $sourcePath,
                'destination' => $destinationPath,
            ];

            event(new StorageError($error));

            Log::error('File copy failed', $error);

            throw $e;
        }
    }

    /**
     * Validate uploaded file
     */
    protected function validateFile(UploadedFile $file): void
    {
        $validationConfig = config('storage.validation');

        // Check file size
        if ($file->getSize() > $validationConfig['max_file_size']) {
            throw new \InvalidArgumentException('File size exceeds maximum allowed size');
        }

        // Check MIME type
        if (!in_array($file->getMimeType(), $validationConfig['allowed_mime_types'])) {
            throw new \InvalidArgumentException('File type not allowed');
        }
    }

    /**
     * Move file from temp path to target folder and create File record
     */
    public function moveFileFromTempToTarget(
        string $tempPath,
        Model $model,
        string $type
    ): File {
        try {
            // Convert URL to local path if needed
            $localPath = $this->convertUrlToLocalPath($tempPath);

            // Validate temp file exists
            if (!$this->filesystem->fileExists($localPath)) {
                throw new \InvalidArgumentException("Source file does not exist: {$localPath}");
            }

            $fileName = basename($localPath);

            // Build target path
            $targetFolder = $this->classNameToFolderName(get_class($model));
            $targetPath = $this->buildFilePath($targetFolder, $fileName);

            if ($this->filesystem->fileExists($targetPath)) {
                throw new \InvalidArgumentException("Target file already exists: {$targetPath}");
            }

            $fileSize = $this->filesystem->fileSize($localPath);

            // Use R2-compatible copy method to avoid ACL issues
            $this->copyFileForR2($localPath, $targetPath);
            $this->filesystem->delete($localPath);

            $file = File::create([
                'filename' => $fileName,
                'path' => $targetPath,
                'size' => $fileSize,
                'fileable_id' => $model->getKey(),
                'fileable_type' => get_class($model),
                'type' => $type
            ]);

            Log::info('File moved from temp to target successfully', [
                'original_temp_path' => $tempPath,
                'local_temp_path' => $localPath,
                'target_path' => $targetPath,
                'file_id' => $file->id,
                'fileable_id' => $model?->getKey(),
                'fileable_type' => $model ? get_class($model) : null,
            ]);

            return $file;

        } catch (\Exception $e) {
            Log::error('Failed to move file from temp to target');

            throw $e;
        }
    }

    /**
     * Convert class name to folder name
     * Example: App\Models\AssessmentMultipleSelect -> assessment-multiple-select
     */
    public function classNameToFolderName(string $className): string
    {
        // Extract class name without namespace
        $classBaseName = class_basename($className);

        // Convert PascalCase to kebab-case
        $folderName = strtolower(preg_replace('/(?<!^)[A-Z]/', '-$0', $classBaseName));

        return $folderName;
    }
}
