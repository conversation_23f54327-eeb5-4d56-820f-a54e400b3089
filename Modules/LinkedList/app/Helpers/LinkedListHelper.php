<?php

namespace Modules\LinkedList\Helpers;

use <PERSON><PERSON>les\LinkedList\Models\LinkedList;
use Modules\LinkedList\Models\LinkedNode;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Model;

class LinkedListHelper
{
    public function createList(Model $entity): LinkedList
    {
        return $entity->list()->create();
    }

    public function append(LinkedList $list, Model $entity): LinkedNode
    {
        return DB::transaction(function () use ($list, $entity) {
            $new = $entity->node()->create([
                'list_id' => $list->id,
                'prev_id' => $list->tail_id,
                'next_id' => null,
            ]);

            if (is_null($list->head_id)) {
                $list->update(['head_id' => $new->id, 'tail_id' => $new->id]);
            } else {
                LinkedNode::where('id', $list->tail_id)->lockForUpdate()->firstOrFail()
                    ->update(['next_id' => $new->id]);
                $list->update(['tail_id' => $new->id]);
            }

            return $new;
        });
    }

    public function prepend(LinkedList $list, Model $entity): LinkedNode
    {
        return DB::transaction(function () use ($list, $entity) {
            $new = $entity->node()->create([
                'list_id' => $list->id,
                'prev_id' => null,
                'next_id' => $list->head_id,
            ]);

            if (is_null($list->head_id)) {
                $list->update(['head_id' => $new->id, 'tail_id' => $new->id]);
            } else {
                $head = LinkedNode::where('id', $list->head_id)->lockForUpdate()->firstOrFail();
                $head->update(['prev_id' => $new->id]);
                $list->update(['head_id' => $new->id]);
            }

            return $new;
        });
    }

    public function insertAfter(LinkedNode $node, Model $entity): LinkedNode
    {
        return DB::transaction(function () use ($node, $entity) {
            $nextId = $node->next_id;

            $new = $entity->node()->create([
                'list_id' => $node->list_id,
                'prev_id' => $node->id,
                'next_id' => $nextId,
            ]);

            $node->update(['next_id' => $new->id]);

            if ($nextId) {
                LinkedNode::where('id', $nextId)->lockForUpdate()->firstOrFail()
                    ->update(['prev_id' => $new->id]);
            } else {
                LinkedList::where('id', $node->list_id)->lockForUpdate()->firstOrFail()
                    ->update(['tail_id' => $new->id]);
            }

            return $new;
        });
    }

    public function insertBefore(LinkedNode $node, Model $entity): LinkedNode
    {
        return DB::transaction(function () use ($node, $entity) {
            $prevId = $node->prev_id;

            $new = $entity->node()->create([
                'list_id' => $node->list_id,
                'prev_id' => $prevId,
                'next_id' => $node->id,
            ]);

            $node->update(['prev_id' => $new->id]);

            if ($prevId) {
                LinkedNode::where('id', $prevId)->lockForUpdate()->firstOrFail()
                    ->update(['next_id' => $new->id]);
            } else {
                LinkedList::where('id', $node->list_id)->lockForUpdate()->firstOrFail()
                    ->update(['head_id' => $new->id]);
            }

            return $new;
        });
    }

    public function remove(LinkedNode $target): void
    {
        DB::transaction(function () use ($target) {
            $list = LinkedList::where('id', $target->list_id)->lockForUpdate()->firstOrFail();

            if ($target->prev_id) {
                LinkedNode::where('id', $target->prev_id)->lockForUpdate()->firstOrFail()
                    ->update(['next_id' => $target->next_id]);
            } else {
                $list->update(['head_id' => $target->next_id]);
            }

            if ($target->next_id) {
                LinkedNode::where('id', $target->next_id)->lockForUpdate()->firstOrFail()
                    ->update(['prev_id' => $target->prev_id]);
            } else {
                $list->update(['tail_id' => $target->prev_id]);
            }

            $target->delete();
        });
    }

    public function moveAfter(LinkedNode $a, LinkedNode $b): void
    {
        if ($a->list_id !== $b->list_id) throw new \InvalidArgumentException('Nodes khác list.');
        if ($a->id === $b->id) return;

        DB::transaction(function () use ($a, $b) {
            $list = LinkedList::where('id', $a->list_id)->lockForUpdate()->firstOrFail();

            $isAlreadyAfterB = ($a->prev_id === $b->id);
            if ($isAlreadyAfterB) return;

            // 1) Remove A from current position
            if ($a->prev_id) {
                LinkedNode::where('id', $a->prev_id)->lockForUpdate()->firstOrFail()
                    ->update(['next_id' => $a->next_id]);
            } else {
                $list->update(['head_id' => $a->next_id]);
            }
            if ($a->next_id) {
                LinkedNode::where('id', $a->next_id)->lockForUpdate()->firstOrFail()
                    ->update(['prev_id' => $a->prev_id]);
            } else {
                $list->update(['tail_id' => $a->prev_id]);
            }

            // 2) Insert A after B
            $a->refresh();
            $b->refresh();

            $a->update([
                'prev_id' => $b->id,
                'next_id' => $b->next_id,
            ]);

            if ($b->next_id) {
                LinkedNode::where('id', $b->next_id)->lockForUpdate()->firstOrFail()
                    ->update(['prev_id' => $a->id]);
            } else {
                $list->update(['tail_id' => $a->id]);
            }

            $b->update(['next_id' => $a->id]);
        });
    }

    public function moveBefore(LinkedNode $a, LinkedNode $b): void
    {
        if ($a->list_id !== $b->list_id) throw new \InvalidArgumentException('Nodes khác list.');
        if ($a->id === $b->id) return;

        DB::transaction(function () use ($a, $b) {
            $list = LinkedList::where('id', $a->list_id)->lockForUpdate()->firstOrFail();

            // nếu A đang ngay trước B thì thôi
            $isAlreadyBeforeB = ($a->next_id === $b->id);
            if ($isAlreadyBeforeB) return;

            // 1) Remove A from current position
            if ($a->prev_id) {
                LinkedNode::where('id', $a->prev_id)->lockForUpdate()->firstOrFail()
                    ->update(['next_id' => $a->next_id]);
            } else {
                $list->update(['head_id' => $a->next_id]);
            }
            if ($a->next_id) {
                LinkedNode::where('id', $a->next_id)->lockForUpdate()->firstOrFail()
                    ->update(['prev_id' => $a->prev_id]);
            } else {
                $list->update(['tail_id' => $a->prev_id]);
            }

            // 2) Insert A before B
            $a->refresh();
            $b->refresh();

            $a->update([
                'prev_id' => $b->prev_id,
                'next_id' => $b->id,
            ]);

            if ($b->prev_id) {
                LinkedNode::where('id', $b->prev_id)->lockForUpdate()->firstOrFail()
                    ->update(['next_id' => $a->id]);
            } else {
                $list->update(['head_id' => $a->id]);
            }

            $b->update(['prev_id' => $a->id]);
        });
    }

    public function toArrayForward(LinkedList $list): array
    {
        $nodes = LinkedNode::query()
            ->with('nodeable')
            ->where('list_id', $list->id)
            ->get(['id','prev_id','next_id','nodeable_type','nodeable_id'])
            ->keyBy('id');

        $out = [];
        $seen = [];
        $id  = $list->head_id;

        // For safety, prevent infinite loop
        $maxSteps = max(0, $nodes->count());

        for ($step = 0; $step < $maxSteps && $id !== null; $step++) {
            if (!isset($nodes[$id])) {
                $out[] = ['_error' => "Broken pointer at $id"];
                break;
            }

            if (isset($seen[$id])) {
                $out[] = ['_error' => "Cycle detected at $id"];
                break;
            }
            $seen[$id] = true;

            $n = $nodes[$id];
            $out[] = [
                'id'      => $n->id,
                'prev_id' => $n->prev_id,
                'next_id' => $n->next_id,
                'data' => $n->nodeable,
            ];

            $id = $n->next_id;
        }

        return $out;
    }

    public function toArrayBackward(LinkedList $list): array
    {
        $nodes = LinkedNode::where('list_id', $list->id)
            ->with('nodeable')
            ->get(['id', 'prev_id', 'next_id', 'payload'])
            ->keyBy('id');

        $out = [];
        $seen = [];
        $id  = $list->tail_id;

        // For safety, prevent infinite loop
        $maxSteps = max(0, $nodes->count());

        for ($step = 0; $step < $maxSteps && $id !== null; $step++) {
            if (!isset($nodes[$id])) {
                $out[] = ['_error' => "Broken pointer at $id"];
                break;
            }

            if (isset($seen[$id])) {
                $out[] = ['_error' => "Cycle detected at $id"];
                break;
            }
            $seen[$id] = true;

            $n = $nodes[$id];
            $out[] = [
                'id'      => $n->id,
                'payload' => $n->payload,
                'prev_id' => $n->prev_id,
                'next_id' => $n->next_id,
                'data' => $n->nodeable,
            ];

            $id = $n->prev_id;
        }

        return $out;
    }

    function buildOrderMap(LinkedList $list): array
    {
        $allNodes = LinkedNode::where('list_id', $list->id)
            ->get(['id','next_id','nodeable_type','nodeable_id'])
            ->keyBy('id');

        $rankMap = [];
        $seen = [];
        $id = $list->head_id;
        $rank = 0;
        $max = $allNodes->count();

        for ($i = 0; $i < $max && $id !== null; $i++) {
            if (!isset($allNodes[$id]) || isset($seen[$id])) break;
            $seen[$id] = true;

            $n = $allNodes[$id];
            if ($n->nodeable_id) {
                $rankMap[$n->nodeable_id] = $rank++;
            }
            $id = $n->next_id;
        }

        return $rankMap;
    }
}
