<?php

namespace Modules\LinkedList\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Modules\LinkedList\Models\LinkedList
 *
 * @property int $id
 * @property int|null $head_id
 * @property int|null $tail_id
 * @property string|null $listable_type
 * @property int|null $listable_id
 * @method static \Illuminate\Database\Eloquent\Builder|LinkedList newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|LinkedList newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|LinkedList query()
 * @method static \Illuminate\Database\Eloquent\Builder|LinkedList whereHeadId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LinkedList whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LinkedList whereTailId($value)
 * @mixin \Eloquent
 */

class LinkedList extends Model
{
    use HasFactory;

    protected $table = 'linked_lists';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'head_id',
        'tail_id',
        'listable_type',
        'listable_id',
    ];

    public function head(): BelongsTo { return $this->belongsTo(LinkedNode::class, 'head_id'); }
    public function tail(): BelongsTo { return $this->belongsTo(LinkedNode::class, 'tail_id'); }
    public function nodes(): HasMany { return $this->hasMany(LinkedNode::class, 'list_id'); }
    public function listable(): BelongsTo { return $this->morphTo(); }
}
