<?php

namespace Modules\LinkedList\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Modules\LinkedList\Models\LinkedNode
 *
 * @property int $id
 * @property int $list_id
 * @property int|null $prev_id
 * @property int|null $next_id
 * @property int|null $nodeable_id
 * @property string|null $nodeable_type
 * @method static \Illuminate\Database\Eloquent\Builder|LinkedNode newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|LinkedNode newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|LinkedNode query()
 * @method static \Illuminate\Database\Eloquent\Builder|LinkedNode whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LinkedNode whereListId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LinkedNode whereNextId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|LinkedNode wherePrevId($value)
 * @mixin \Eloquent
 */

class LinkedNode extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'list_id',
        'prev_id',
        'next_id',
        'nodeable_type',
        'nodeable_id',
    ];

    public function list(): BelongsTo { return $this->belongsTo(LinkedList::class, 'list_id'); }
    public function prev(): BelongsTo { return $this->belongsTo(LinkedNode::class, 'prev_id'); }
    public function next(): BelongsTo { return $this->belongsTo(LinkedNode::class, 'next_id'); }
    public function nodeable(): BelongsTo { return $this->morphTo(); }
}
