<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('link_lists', function (Blueprint $table) {
            $table->foreign('head_id')->references('id')->on('nodes')->nullOnDelete();
            $table->foreign('tail_id')->references('id')->on('nodes')->nullOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('link_lists', function (Blueprint $table) {
            $table->dropForeign(['head_id']);
            $table->dropForeign(['tail_id']);
        });
    }
};
