<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('linked_lists', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('head_id')->nullable()->index();
            $table->unsignedBigInteger('tail_id')->nullable()->index();

            $table->nullableMorphs('listable');
            $table->unique(['listable_type','listable_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('linked_lists');
    }
};
