<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('linked_nodes', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('list_id')->index();
            $table->unsignedBigInteger('prev_id')->nullable()->unique();
            $table->unsignedBigInteger('next_id')->nullable()->unique();

            $table->nullableMorphs('nodeable');
            $table->unique(['nodeable_type','nodeable_id']);

            $table->foreign('list_id')->references('id')->on('lists')->cascadeOnDelete();
            $table->foreign('prev_id')->references('id')->on('nodes')->nullOnDelete();
            $table->foreign('next_id')->references('id')->on('nodes')->nullOnDelete();

            $table->check('(prev_id IS NULL OR prev_id <> id)');
            $table->check('(next_id IS NULL OR next_id <> id)');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('linked_nodes');
    }
};
