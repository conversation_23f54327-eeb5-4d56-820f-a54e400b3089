<?php

namespace App\Traits;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Model;

trait UsePosition
{
    protected static function booted()
    {
        static::addGlobalScope('positionAsc', function (Builder $builder) {
            $builder->orderBy('position', 'asc')->orderBy('id', 'asc');
        });

        // Nếu không truyền position khi tạo -> tự set vào cuối
        static::creating(function (Model $model) {
            if (is_null($model->position)) {
                $max = DB::table($model->getTable())->max('position');
                $model->position = is_null($max) ? 1 : $max + 1;
            }
        });
    }

    /**
     * Chèn bản ghi vào vị trí $position (1-indexed). Nếu null -> thêm cuối.
     */
    public static function createAtPosition(array $attributes, ?int $position = null): self
    {
        return DB::transaction(function () use ($attributes, $position) {
            $table = (new static)->getTable();
            $count = DB::table($table)->count();

            // Nếu không truyền vị trí -> thêm cuối
            if ($position === null) {
                $attributes['position'] = $count + 1;
                return static::create($attributes);
            }

            // Chuẩn hóa vị trí trong [1, $count+1]
            $position = max(1, min($position, $count + 1));

            // Dịch các phần tử >= position xuống 1
            DB::table($table)
                ->where('position', '>=', $position)
                ->update(['position' => DB::raw('position + 1')]);

            $attributes['position'] = $position;
            return static::create($attributes);
        });
    }

    /**
     * Di chuyển bản ghi hiện tại đến vị trí $newPosition (1-indexed).
     */
    public function moveToPosition(int $newPosition): self
    {
        return DB::transaction(function () use ($newPosition) {
            $table = $this->getTable();

            $count = DB::table($table)->count();
            $newPosition = max(1, min($newPosition, $count)); // không thể lớn hơn số phần tử hiện tại

            $oldPosition = (int) $this->position;
            if ($newPosition === $oldPosition) {
                return $this; // không cần làm gì
            }

            if ($newPosition < $oldPosition) {
                // Ví dụ từ 8 -> 3: các phần tử [3..7] +1
                DB::table($table)
                    ->whereBetween('position', [$newPosition, $oldPosition - 1])
                    ->update(['position' => DB::raw('position + 1')]);
            } else {
                // Ví dụ từ 3 -> 8: các phần tử [4..8] -1
                DB::table($table)
                    ->whereBetween('position', [$oldPosition + 1, $newPosition])
                    ->update(['position' => DB::raw('position - 1')]);
            }

            // Cập nhật vị trí cho bản ghi hiện tại
            $this->position = $newPosition;
            $this->save();

            return $this;
        });
    }

    public function deleteAndReorder(): bool
    {
        return DB::transaction(function () {
            $table = $this->getTable();
            $pos = $this->position;

            $deleted = parent::delete();

            if ($deleted) {
                DB::table($table)
                    ->where('position', '>', $pos)
                    ->update(['position' => DB::raw('position - 1')]);
            }

            return $deleted;
        });
    }
}
